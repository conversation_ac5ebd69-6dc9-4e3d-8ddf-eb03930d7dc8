<template>
  <div class="realty-game-results-shareable">
    <div class="container q-pa-md">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner-dots color="primary"
                        size="3em" />
        <div class="text-h6 q-mt-md">Loading results...</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="text-h6 q-mt-md text-negative">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults"
               class="q-mt-md" />
      </div>

      <!-- Results Content -->
      <div v-else-if="results"
           class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <RealtyGamePerformanceBadge :player-results="playerResults" />

          <h1 class="text-h4 text-weight-bold text-primary q-mb-lg">
            {{ ssGameSession.game_player_nickname }}'s Property Price Challenge Results
          </h1>

          <div class="text-h6 text-grey-7 q-mb-lg">
            {{ ssGameSession.game_title || 'Property Price Challenge' }}
          </div>

          <!-- Privacy Notice -->
          <!-- <q-banner class="bg-info text-white q-mb-lg" rounded>
            <template v-slot:avatar>
              <q-icon name="privacy_tip" />
            </template>
This page shows performance results without revealing actual property prices.
Perfect for sharing!
</q-banner> -->
        </div>

        <!-- Performance Summary -->
        <!-- <RealtyGameSummaryCard 
          :player-results="playerResults"
          :game-breakdown="gameBreakdown"
          :comparison-summary="comparisonSummary"
          :is-current-user-session="isCurrentUserSession"
          :player-name="ssGameSession.game_player_nickname" /> -->

        <!-- Results Table (without prices) -->
        <!-- <RealtyGameResultsTable 
          :game-breakdown="gameBreakdown"
          :show-prices="false"
          :is-current-user-session="isCurrentUserSession"
          :player-name="ssGameSession.game_player_nickname"
          :format-price="formatPrice"
          :get-score-color="getScoreColor" /> -->

        <!-- Share Information -->
        <q-card class="share-info-card q-mb-xl"
                flat
                bordered>
          <q-card-section class="q-pa-lg text-center">
            <div class="text-h6 q-mb-md">
              <q-icon name="share"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              Share This Page
            </div>
            <!-- <div class="text-body1 q-mb-lg">
              This page is perfect for sharing! It shows {{ ssGameSession.game_player_nickname }}'s
              performance without revealing the actual property prices.
            </div>
            <q-btn color="primary"
                   icon="share"
                   label="Share This Page"
                   @click="shareResults"
                   size="lg" /> -->

            <SocialSharing :socialSharingPrompt="`This page is made for sharing! It shows ${ssGameSession.game_player_nickname }'s
              performance without revealing the actual property prices.`"
                           socialSharingTitle="Check out how I did in this property price game"
                           :urlProp="shareableResultsUrl">
            </SocialSharing>
          </q-card-section>
        </q-card>

        <!-- Link to Full Results -->
        <q-card v-if="isCurrentUserSession"
                class="full-results-card q-mb-xl"
                flat
                bordered>
          <q-card-section class="q-pa-lg text-center">
            <div class="text-h6 q-mb-md">
              <q-icon name="lock"
                      color="warning"
                      size="sm"
                      class="q-mr-sm" />
              Want to See the Actual Prices?
            </div>
            <div class="text-body1 q-mb-lg">
              Play the game first - you will then get your own results page with insights about the property prices ;)
            </div>
            <!-- <q-btn color="info"
                   icon="visibility"
                   label="View Full Results"
                   @click="goToDetailedPage"
                   size="lg" /> -->
          </q-card-section>
        </q-card>

        <!-- Action Buttons -->
        <!-- <RealtyGameActionButtons 
          :show-shareable-link="false"
          :show-detailed-link="isCurrentUserSession"
          share-button-label="Share This Page"
          @share="shareResults"
          @view-detailed="goToDetailedPage"
          @play-again="startNewGame"
          @start-new="startNewGame" /> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import { useServerRealtyGameResults } from '../composables/useServerRealtyGameResults'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'
import RealtyGamePerformanceBadge from '../components/RealtyGamePerformanceBadge.vue'
// import RealtyGameSummaryCard from '../components/RealtyGameSummaryCard.vue'
// import RealtyGameResultsTable from '../components/RealtyGameResultsTable.vue'
// import RealtyGameActionButtons from '../components/RealtyGameActionButtons.vue'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'

const props = defineProps({
  gameSessionId: {
    type: String,
    required: true,
  },
  gameCommunitiesDetails: {
    type: Object,
  },
})

const $router = useRouter()
const $route = useRoute()
const $q = useQuasar()

const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
} = useServerRealtyGameResults()

const { getCurrentSessionId } = useRealtyGameStorage()
const { formatPrice } = useCurrencyConverter()

// Computed properties
const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === props.gameSessionId
})

const shareableResultsUrl = computed(() => {
  let shareRoute = {
    name: 'rPriceGameResultsShareable',
    params: {
      gameSessionId: props.gameSessionId,
      gameSlug: $route.params.gameSlug
    }
  }
  let fullPath = `${location.origin}${$router.resolve(shareRoute).href}`
  // window.open(fullPath, "_blank")
  return fullPath
})
// Methods
const loadResults = async () => {
  await fetchResults(
    props.gameSessionId,
    $router.currentRoute.value.params.gameSlug
  )
}

const shareResults = () => {
  const url = window.location.href
  const text = `Check out ${ssGameSession.value.game_player_nickname}'s Property Price Challenge results! Score: ${playerResults.value.total_score}/${playerResults.value.max_possible_score} (${playerResults.value.performance_rating.rating})`

  if (navigator.share) {
    navigator.share({
      title: `${ssGameSession.value.game_player_nickname}'s Property Price Challenge Results`,
      text: text,
      url: url
    }).catch(() => {
      fallbackShare(url, text)
    })
  } else {
    fallbackShare(url, text)
  }
}

const fallbackShare = (url, text) => {
  navigator.clipboard.writeText(`${text}\n${url}`).then(() => {
    $q.notify({
      type: 'positive',
      message: 'Results link copied to clipboard!',
      position: 'top'
    })
  }).catch(() => {
    $q.notify({
      type: 'info',
      message: 'Share this link: ' + url,
      position: 'top',
      timeout: 5000
    })
  })
}

const goToDetailedPage = () => {
  $router.push({
    name: 'rPriceGameResultsDetailed',
    params: {
      gameSessionId: props.gameSessionId,
      gameSlug: $route.params.gameSlug
    }
  })
}

const startNewGame = () => {
  $router.push({ name: 'rPriceGuessStart' })
}

// Lifecycle
onMounted(() => {
  loadResults()
})
</script>

<style scoped>
.container {
  max-width: 1000px;
  margin: 0 auto;
}

.share-info-card,
.full-results-card {
  border-radius: 12px;
}

.share-info-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.full-results-card {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}
</style>
